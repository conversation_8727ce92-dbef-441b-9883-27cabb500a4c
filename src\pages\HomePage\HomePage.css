.homepage {
  position: relative;
  min-height: calc(100vh - 80px);
  overflow: hidden;
}

.hero-section {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #1a252f 100%);
  min-height: calc(100vh - 80px);
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
  padding: 2rem;
}

.hero-content {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  width: 100%;
}

.hero-text {
  color: white;
  z-index: 2;
}

.hero-number {
  font-size: 8rem;
  font-weight: bold;
  color: rgba(255, 255, 255, 0.1);
  line-height: 1;
  margin-bottom: -2rem;
  font-family: 'Arial', sans-serif;
}

.hero-subtitle {
  font-size: 0.9rem;
  letter-spacing: 2px;
  color: #a8e6cf;
  margin-bottom: 1rem;
  font-weight: 500;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: bold;
  line-height: 1.1;
  margin-bottom: 2rem;
  color: white;
}

.cta-button {
  background: linear-gradient(135deg, #a8e6cf 0%, #88d8a3 100%);
  color: #2c3e50;
  border: none;
  padding: 1rem 2rem;
  font-size: 0.9rem;
  font-weight: 600;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  border-radius: 0;
}

.cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(168, 230, 207, 0.4);
}

.hero-image {
  position: relative;
  z-index: 1;
}

.skier-image {
  width: 100%;
  height: auto;
  max-height: 500px;
  object-fit: cover;
  border-radius: 10px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.hero-navigation {
  position: absolute;
  bottom: 2rem;
  right: 2rem;
  display: flex;
  align-items: center;
  gap: 2rem;
}

.nav-dots {
  display: flex;
  gap: 0.5rem;
}

.dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.dot.active {
  background-color: #a8e6cf;
  transform: scale(1.2);
}

.nav-arrows {
  display: flex;
  gap: 0.5rem;
}

.nav-arrow {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.2rem;
}

.nav-arrow:hover {
  background: rgba(168, 230, 207, 0.2);
  border-color: #a8e6cf;
}

.side-navigation {
  position: fixed;
  left: 2rem;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 1rem;
  z-index: 1000;
}

.side-nav-item {
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.side-nav-item:hover {
  background: rgba(168, 230, 207, 0.2);
  border-color: #a8e6cf;
  transform: translateX(5px);
}

.side-nav-icon {
  font-size: 1.2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-content {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }
  
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-number {
    font-size: 6rem;
  }
  
  .side-navigation {
    display: none;
  }
  
  .hero-navigation {
    bottom: 1rem;
    right: 1rem;
  }
}

@media (max-width: 480px) {
  .hero-section {
    padding: 1rem;
  }
  
  .hero-title {
    font-size: 2rem;
  }
  
  .hero-number {
    font-size: 4rem;
  }
}
