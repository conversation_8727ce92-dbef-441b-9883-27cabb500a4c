import React from 'react';
import './HomePage.css';

const HomePage: React.FC = () => {
  return (
    <div className="homepage">
      {/* Hero Section */}
      <section className="hero-section">
        <div className="hero-content">
          <div className="hero-text">
            <div className="hero-number">01</div>
            <div className="hero-subtitle">SKIING BEYOND YOUR LIMIT</div>
            <h1 className="hero-title">
              KNOW YOUR<br />
              LIMITS. SKI<br />
              BEYOND THEM.
            </h1>
            <button className="cta-button">VIEW MORE</button>
          </div>
          <div className="hero-image">
            <img
              src="/api/placeholder/600/400"
              alt="Professional skier in action"
              className="skier-image"
            />
          </div>
        </div>

        <div className="hero-navigation">
          <div className="nav-dots">
            <span className="dot active"></span>
            <span className="dot"></span>
            <span className="dot"></span>
          </div>
          <div className="nav-arrows">
            <button className="nav-arrow prev">‹</button>
            <button className="nav-arrow next">›</button>
          </div>
        </div>
      </section>

      <div className="side-navigation">
        <div className="side-nav-item">
          <span className="side-nav-icon">📧</span>
        </div>
        <div className="side-nav-item">
          <span className="side-nav-icon">📱</span>
        </div>
        <div className="side-nav-item">
          <span className="side-nav-icon">📍</span>
        </div>
        <div className="side-nav-item">
          <span className="side-nav-icon">🔗</span>
        </div>
      </div>
    </div>
  );
};

export default HomePage;