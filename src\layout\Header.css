.header {
  background: linear-gradient(135deg, #a8e6cf 0%, #88d8a3 100%);
  padding: 1rem 0;
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 2rem;
}

.logo h1 {
  font-size: 1.8rem;
  font-weight: bold;
  color: #2c3e50;
  margin: 0;
  line-height: 1.2;
  letter-spacing: 1px;
}

.nav-menu ul {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 2rem;
}

.nav-link {
  text-decoration: none;
  color: #2c3e50;
  font-weight: 500;
  font-size: 0.95rem;
  letter-spacing: 0.5px;
  transition: color 0.3s ease;
  position: relative;
}

.nav-link:hover,
.nav-link.active {
  color: #27ae60;
}

.nav-link.active::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #27ae60;
}

.header-icons {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.icon-btn {
  background: none;
  border: none;
  color: #2c3e50;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
  color: #27ae60;
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-container {
    padding: 0 1rem;
  }
  
  .nav-menu ul {
    gap: 1rem;
  }
  
  .nav-link {
    font-size: 0.9rem;
  }
  
  .logo h1 {
    font-size: 1.5rem;
  }
}

@media (max-width: 600px) {
  .nav-menu {
    display: none;
  }
  
  .header-container {
    justify-content: space-between;
  }
}
